/* OBFUSCATED BY CSSOBFUSCATOR.COM at 2025/01/08 21:21:44 */
@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@100..900&display=swap");
[class~="izeyy-notification-container"],
[class~="izeyy-notification"][class~="stacked"]::after,
[class~="izeyy-notification"] [class~="notification-progress"],
[class~="izeyy-notification"] {
  position: absolute;
}
body {
  position: relative;
}
[class~="izeyy-notification-container"] {
  width: 13.5vw;
}
[class~="izeyy-notification-container"] {
  top: auto;
}
[class~="izeyy-notification-container"][class~="notification-container-bottomleft"] {
  left: 21.375pt;
}
body {
  top: 0.208333333in;
}
[class~="izeyy-notification-container"] {
  left: auto;
}
[class~="izeyy-notification"][class~="info"],
[class~="izeyy-notification"][class~="error"],
.izeyy-notification.warning,
[class~="izeyy-notification"][class~="warning"] [class~="notification-title"],
.izeyy-notification .s,
[class~="izeyy-notification"][class~="success"] [class~="notification-title"],
[class~="izeyy-notification"][class~="default"] [class~="notification-title"],
[class~="izeyy-notification"][class~="info"] [class~="notification-title"],
[class~="izeyy-notification"],
[class~="izeyy-notification"][class~="error"] [class~="notification-title"],
[class~="izeyy-notification"][class~="success"],
[class~="notification-title"] {
  color: #fff;
}
[class~="izeyy-notification-container"][class~="notification-container-bottomleft"] {
  bottom: 24.25vh;
}
[class~="izeyy-notification-container"] {
  bottom: auto;
}
[class~="izeyy-notification"][class~="success"] {
  background: rgba(163, 255, 18, 0.6);
}
[class~="izeyy-notification-container"] {
  right: auto;
}
body,
[class~="izeyy-compo"] {
  margin-left: 0;
}
[class~="izeyy-compo"],
body {
  margin-bottom: 0;
}
[class~="izeyy-notification-container"] {
  z-index: 10000;
}
[class~="izeyy-notification"] {
  right: -1pc;
}
[class~="izeyy-notification"] {
  width: 14.5vw;
}
[class~="izeyy-notification"] {
  opacity: 0;
}
[class~="izeyy-notification"][class~="stacked"]::after {
  content: attr(data-count);
}
[class~="izeyy-notification"],
[class~="notification-title"] {
  font-size: 18px;
}
[class~="izeyy-notification"] {
  border-radius: 0px;
}
[class~="izeyy-compo"],
body {
  margin-right: 0;
}
[class~="izeyy-notification"][class~="stacked"]::after {
  right: 10px;
}
[class~="izeyy-notification"][class~="info"] {
  background: rgba(0, 144, 255, 0.6);
}
[class~="izeyy-notification"] {
  background: linear-gradient(
    90deg,
    rgba(12, 16, 21, 0.5) 0%,
    rgba(12, 16, 21, 0.25) 100%
  );
}
[class~="izeyy-notification"] {
  border-top-left-radius: 3.5px;
}
[class~="izeyy-notification"] {
  border-top-right-radius: 3.5px;
}
.izeyy-notification.warning {
  background: rgba(255, 204, 0, 0.6);
}
[class~="izeyy-notification"] {
  transform: scale(1);
}
[class~="izeyy-notification"],
[class~="notification-header"] {
  font-weight: 500;
}
[class~="izeyy-notification"],
[class~="notification-header"] {
  font-family: Outfit;
}
[class~="izeyy-notification"][class~="stacked"]::after {
  bottom: 10px;
}
[class~="izeyy-compo"],
body {
  margin-top: 0;
}
[class~="izeyy-compo"] {
  padding-left: 0.125in;
}
[class~="izeyy-compo"] {
  padding-bottom: 0px;
}
[class~="izeyy-compo"] {
  padding-right: 0.125in;
}
[class~="izeyy-compo"] {
  padding-top: 10px;
}
[class~="izeyy-notification"][class~="error"] {
  background: rgba(239, 7, 93, 0.6);
}
#caca_container,
body {
  width: 100vw;
}
body,
#caca_container {
  height: 100vh;
}
[class~="notification-title"] {
  line-height: 40px;
}
#caca_container,
[class~="notif-title"],
[class~="notification-header"],
body {
  display: flex;
}
[class~="notification-header"] {
  font-size: 0.78125pc;
}
[class~="izeyy-notification"][class~="with-progress"] {
  padding-bottom: 0.75pc;
}
[class~="notification-header"] {
  line-height: 9.375pt;
}
[class~="izeyy-notification"][class~="stacked"]::after,
[class~="notification-title-component"],
body,
#caca_container {
  align-items: center;
}
[class~="izeyy-notification"] [class~="notification-progress"] {
  bottom: 0;
}
[class~="izeyy-notification"][class~="stacked"]::after {
  width: 25px;
}
[class~="notification-title-component"],
[class~="notification-header"],
[class~="notif-title"] {
  flex-direction: row;
}
[class~="notification-header"],
[class~="notif-title"] {
  justify-content: space-between;
}
[class~="notification-subject"],
[class~="izeyy-notification"][class~="stacked"]::after {
  height: 1.5625pc;
}
[class~="notification-subject"] {
  width: 0.885416667in;
}
[class~="izeyy-notification"] [class~="notification-progress"] {
  left: 0;
}
body {
  justify-content: center;
}
[class~="izeyy-notification"] [class~="notification-progress"],
[class~="izeyy-notification"]
  [class~="notification-progress"]
  [class~="notification-bar"] {
  width: 100%;
}
[class~="notification-subject"] {
  background-color: #835896;
}
[class~="notification-subject"] {
  opacity: 1;
}
[class~="notification-subject"] {
  font-size: 0.109375in;
}
[class~="izeyy-notification"]
  [class~="notification-progress"]
  [class~="notification-bar"] {
  height: 0.041666667in;
}
[class~="notification-subject"] {
  line-height: 25px;
}
[class~="izeyy-notification"] [class~="notification-progress"] {
  height: 0.125pc;
}
[class~="notification-subject"] {
  padding-left: 9px;
}
[class~="notification-subject"] {
  font-weight: 700;
}
[class~="izeyy-notification"] [class~="notification-progress"] {
  background-color: rgba(255, 255, 255, 0);
}
[class~="notif-title"] {
  margin-left: 1.28125pc;
}
[class~="notification-icon"],
body {
  overflow: hidden;
}
[class~="notification-message"],
[class~="notif-title"] {
  margin-bottom: 0.3125pc;
}
[class~="notif-title"] {
  margin-right: 1.28125pc;
}
[class~="notif-title"] {
  margin-top: 1.28125pc;
}
.izeyy-notification .s,
.izeyy-notification .w {
  font-weight: normal;
}
[class~="izeyy-notification"][class~="stacked"]::after {
  font-size: 0.75pc;
}
[class~="izeyy-notification"]
  [class~="notification-progress"]
  [class~="notification-bar"] {
  background-color: #fff;
}
[class~="izeyy-notification"]
  [class~="notification-progress"]
  [class~="notification-bar"] {
  box-shadow: 0 0 7.5pt rgba(255, 255, 255, 0.6),
    0 0 15pt rgba(255, 255, 255, 0.4), 0 0 30px rgba(255, 255, 255, 0.2);
}
[class~="notif-title"] {
  align-content: center;
}
[class~="notif-title"] {
  font-weight: 500;
}
[class~="notif-title"],
[class~="notification-message"] {
  font-family: Outfit;
}
[class~="izeyy-notification"][class~="stacked"]::after,
[class~="notif-title"] {
  color: #fff;
}
[class~="notif-title"] {
  width: calc(100% - 25px);
}
[class~="izeyy-notification"][class~="stacked"]::after,
[class~="notification-title-component"] {
  display: flex;
}
[class~="notification-icon"] {
  width: 50px;
}
[class~="notification-icon"] {
  height: 0.520833333in;
}
[class~="notification-icon"] {
  float: left;
}
[class~="notification-icon"] {
  margin-right: 0.28125pc;
}
.izeyy-notification .r {
  color: #f00;
}
[class~="izeyy-notification"] [class~="g"] {
  color: #6bbe6a;
}
[class~="izeyy-notification"] [class~="b"] {
  color: #09c;
}
[class~="izeyy-notification"]
  [class~="notification-progress"]
  [class~="notification-bar"] {
  transform: scale(0, 1);
}
[class~="izeyy-notification"][class~="stacked"]::after {
  font-weight: 900;
}
[class~="notification-icon"] {
  border-radius: 0.03125in;
}
[class~="notification-icon"] {
  margin-top: -1.5pt;
}
[class~="izeyy-notification"]
  [class~="notification-progress"]
  [class~="notification-bar"] {
  transform-origin: 0 0 0;
}
[class~="izeyy-notification"] [class~="y"] {
  color: #fffb00;
}
[class~="notification-message"] {
  margin-top: 6pt;
}
[class~="izeyy-notification"] [class~="o"] {
  color: #fa9f0c;
}
[class~="notification-message"] {
  font-size: 0.15625in;
}
[class~="izeyy-notification"] [class~="p"] {
  color: #b23de4;
}
.izeyy-notification .c {
  color: #727272;
}
.notification-banner {
  width: 100%;
}
.notification-banner {
  height: 75px;
}
.notification-banner {
  border-top-left-radius: 0.46875pc;
}
.notification-banner {
  border-top-right-radius: 0.46875pc;
}
.notification-banner {
  margin-bottom: -0.052083333in;
}
#caca_container,
[class~="izeyy-notification"][class~="stacked"]::after {
  justify-content: center;
}
[class~="izeyy-notification"][class~="stacked"]::after {
  border-radius: 0.260416667in;
}
@keyframes rgb {
  0% {
    color: #f00;
  }
  10% {
    color: #ff7300;
  }
  20% {
    color: #ffd900;
  }
  30% {
    color: #a6ff00;
  }
  40% {
    color: #15ff00;
  }
  50% {
    color: #0f8;
  }
  60% {
    color: #0cf;
  }
  70% {
    color: #00a2ff;
  }
  80% {
    color: #0026ff;
  }
  90% {
    color: #a200ff;
  }
  100% {
    color: #f07;
  }
}
@keyframes anonymous {
  0% {
    color: #00ff0d;
  }
  10% {
    color: #00ff0d;
  }
  20% {
    color: #fff;
  }
  30% {
    color: #fff;
  }
  40% {
    color: #00ff0d;
  }
  50% {
    color: #00ff0d;
  }
  60% {
    color: #fff;
  }
  70% {
    color: #fff;
  }
  80% {
    color: #00ff0d;
  }
  90% {
    color: #00ff0d;
  }
  100% {
    color: #fff;
  }
}
@keyframes alertred {
  0% {
    color: #f00;
  }
  10% {
    color: #f00;
  }
  20% {
    color: #fff;
  }
  30% {
    color: #fff;
  }
  40% {
    color: #f00;
  }
  50% {
    color: #f00;
  }
  60% {
    color: #fff;
  }
  70% {
    color: #fff;
  }
  80% {
    color: #f00;
  }
  90% {
    color: #f00;
  }
  100% {
    color: #fff;
  }
}
[class~="izeyy-notification"] [class~="t"] {
}
[class~="izeyy-notification"] [class~="a"] {
  animation: anonymous 5s linear infinite;
}
[class~="izeyy-notification"][class~="active"][class~="progress"]
  [class~="notification-bar"] {
  animation-name: progress;
}
[class~="izeyy-notification"][class~="active"][class~="progress"]
  [class~="notification-bar"] {
  animation-timing-function: linear;
}
[class~="izeyy-notification"] [class~="v"] {
  animation: alertred 5s linear infinite;
}
[class~="izeyy-notification"] [class~="u"] {
  font-weight: normal;
}
[class~="izeyy-notification"] [class~="u"] {
  text-decoration: underline;
}
[class~="izeyy-notification"] [class~="h"] {
  font-weight: bold;
}
[class~="izeyy-notification"][class~="hiding"] [class~="notification-bar"] {
  width: 0%;
}
@keyframes qsdqjkdqsjdirqsdqsdq {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes fadeOutLeft {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
  }
}
@keyframes flipOutX {
  from {
    transform: perspective(400px);
  }
  30% {
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1;
  }
  to {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0;
  }
}
@keyframes flipOutY {
  from {
    transform: perspective(400px);
  }
  30% {
    transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    opacity: 1;
  }
  to {
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    opacity: 0;
  }
}
@keyframes bounceOutLeft {
  20% {
    opacity: 1;
    transform: translate3d(20px, 0, 0) scaleX(0.9);
  }
  to {
    opacity: 0;
    transform: translate3d(-2000px, 0, 0) scaleX(2);
  }
}
@keyframes backOutLeft {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  20% {
    transform: translateX(0px) scale(0.7);
    opacity: 0.7;
  }
  100% {
    transform: translateX(-2000px) scale(0.7);
    opacity: 0.7;
  }
}
@keyframes slideOutLeft {
  from {
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    transform: translate3d(-120%, 0, 0);
  }
}
@keyframes qsdqjkdqsjioklfqklqska {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}
@keyframes qsdqjkdqsjioklfqklqskaLeft {
  40% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
  }
  to {
    opacity: 0;
    transform: scale(0.1) translate3d(-2000px, 0, 0);
  }
}
@keyframes progress {
  from {
    transform: scale(0, 1);
  }
  to {
    transform: scale(1, 1);
  }
}
