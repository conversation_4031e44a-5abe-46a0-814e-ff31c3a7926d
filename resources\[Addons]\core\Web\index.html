<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fuksus Loading</title>
    <link rel="stylesheet" href="style/main.css">
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="script/main.js"></script>
</head>
<body x-data="Main" x-init="listen">
    <!-- Mouettes volantes -->
    <div class="seagulls-container">
        <!-- Mouette 1: Vol en cercles -->
        <div class="seagull seagull-1">
            <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg">
                <g class="wings">
                    <path d="M15 15 Q25 8, 35 15" stroke="white" stroke-width="2.5" fill="none" class="wing-left"/>
                    <path d="M25 15 Q35 8, 45 15" stroke="white" stroke-width="2.5" fill="none" class="wing-right"/>
                </g>
                <circle cx="30" cy="16" r="1" fill="white" opacity="0.8"/>
            </svg>
        </div>

        <!-- Mouette 2: Vol plané -->
        <div class="seagull seagull-2">
            <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg">
                <g class="wings">
                    <path d="M12 15 Q22 10, 32 15" stroke="white" stroke-width="2" fill="none" class="wing-left"/>
                    <path d="M28 15 Q38 10, 48 15" stroke="white" stroke-width="2" fill="none" class="wing-right"/>
                </g>
                <circle cx="30" cy="16" r="1" fill="white" opacity="0.7"/>
            </svg>
        </div>

        <!-- Mouette 3: Vol avec battements -->
        <div class="seagull seagull-3">
            <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg">
                <g class="wings">
                    <path d="M10 15 Q20 5, 30 15" stroke="white" stroke-width="2.5" fill="none" class="wing-left"/>
                    <path d="M30 15 Q40 5, 50 15" stroke="white" stroke-width="2.5" fill="none" class="wing-right"/>
                </g>
                <circle cx="30" cy="16" r="1" fill="white" opacity="0.9"/>
            </svg>
        </div>

        <!-- Mouette 4: Vol en spirale -->
        <div class="seagull seagull-4">
            <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg">
                <g class="wings">
                    <path d="M14 15 Q24 9, 34 15" stroke="white" stroke-width="2" fill="none" class="wing-left"/>
                    <path d="M26 15 Q36 9, 46 15" stroke="white" stroke-width="2" fill="none" class="wing-right"/>
                </g>
                <circle cx="30" cy="16" r="1" fill="white" opacity="0.6"/>
            </svg>
        </div>

        <!-- Mouette 5: Vol de chasse -->
        <div class="seagull seagull-5">
            <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg">
                <g class="wings">
                    <path d="M16 15 Q26 7, 36 15" stroke="white" stroke-width="2.5" fill="none" class="wing-left"/>
                    <path d="M24 15 Q34 7, 44 15" stroke="white" stroke-width="2.5" fill="none" class="wing-right"/>
                </g>
                <circle cx="30" cy="16" r="1" fill="white" opacity="0.8"/>
            </svg>
        </div>
    </div>

    <div class="container">
        <img src="img/logo.png" alt="" class="logo">
        <div class="nav">
            <template x-for="button in buttons">
                <div class="button" :class="button.selected ? 'selected':''" x-text="button.label" @click="button.selected = selectBtn(button.selected)">Home</div>
            </template>
        </div>
        <div class="home" x-show="buttons[0].selected" x-transition:enter.duration.500ms>
            <div class="label">💎 Imperial Roleplay</div>
            <div class="desc">
                Plongez dans l’univers d’<strong>Imperial Roleplay</strong>, un monde où chaque décision façonne votre destin. 
                Rejoignez une communauté active et immersive, incarnez le personnage de vos rêves, et écrivez votre propre histoire dans un environnement riche en possibilités. 
                Que vous soyez civil, entrepreneur, force de l’ordre ou hors-la-loi… Ici, votre aventure commence. 🔥👑
            </div>
            <div class="discord-container">
                <div class="discord-bg">
                    <img src="img/discord-logo.svg" alt="">
                </div>
                <div class="label">Discord:</div>
                <div class="member-count" x-text="memberCount + ' members'"></div>
                <a class="btn" :href="DiscordInviteLink" target="_blank" @click="window.invokeNative('openUrl', DiscordInviteLink);">Connect <img src="img/arrow.svg" alt="" x-transition></a>
            </div>
        </div>
        <div class="news" x-show="buttons[1].selected" x-transition:enter.duration.500ms>
            <div class="label2">News</div>
            <div class="desc" style="left: 0; top: 3.5vw;">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt.</div>
            <div class="news-container">
                <div class="news">
                    <div class="date">2021.12.30</div>
                    <div class="label">Winter season update</div>
                    <div class="news-desc">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor.</div>
                </div>
                <div class="news">
                    <div class="date">2021.12.30</div>
                    <div class="label">Winter season update</div>
                    <div class="news-desc">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip</div>
                </div>
                <div class="news">
                    <div class="date">2021.12.30</div>
                    <div class="label">Winter season update</div>
                    <div class="news-desc">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip</div>
                </div>
                <div class="news">
                    <div class="date">2021.12.30</div>
                    <div class="label">Winter season update</div>
                    <div class="news-desc">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor.</div>
                    <img src="img/news.png" alt="">
                </div>
                <div class="news">
                    <div class="date">2021.12.30</div>
                    <div class="label">Winter season update</div>
                    <div class="news-desc">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor.</div>
                </div>
            </div>
        </div>
        <div class="team" x-show="buttons[2].selected" x-transition:enter.duration.500ms>
            <div class="label2">Team</div>
            <div class="desc" style="left: 0; top: 3.5vw;">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt.</div>
            <div class="team-container">
                <template x-for="member in team">
                    <div class="member">
                        <img :src="member.img" alt="">
                        <div class="shadow"></div>
                        <div class="discord" x-text="member.discord"></div>
                        <div class="role" x-text="member.role"></div>
                    </div>
                </template>
            </div>
        </div>
    </div>





    <div class="music-container" :class="musicOpen ? 'active':''">
        <img src="img/music.png" alt="">
        <div class="circle" @click="musicOpen = !musicOpen"> 
            <div class="bar1"></div>
            <div class="bar2" :class="musicOpen ? 'active':''"></div>
        </div>
        <div class="visualizer">
            <div class="bar" :class="isMusicPlaying ? 'active':''"></div>
            <div class="bar" :class="isMusicPlaying ? 'active2':''"></div>
            <div class="bar" :class="isMusicPlaying ? 'active3':''"></div>
        </div>
        <div class="info" x-show="musicOpen" x-transition:enter.duration.3000ms x-transition:leave.duration.400ms>
            
            <div class="label" x-text="musicList[currentSong].label"></div>
            <div class="author" x-text="musicList[currentSong].author"></div>
            <img src="img/back.svg" alt="" class="back" @click="prev()">
            <img src="img/pause.svg" alt="" class="pause" x-show="isMusicPlaying"  @click="pause()">
            <img src="img/play.svg" alt="" class="play" x-show="!isMusicPlaying" @click="play()">
            <img src="img/forward.svg" alt="" class="forward" @click="next()">
        </div>
    </div>


</body>
</html>