// @ts-nocheck
const config=JSON.parse(LoadResourceFile(GetCurrentResourceName(),"config.json")),Delay=e=>new Promise((t=>setTimeout(t,e)));let cam,camInfo,ped,interval;const playerId=PlayerId();let QBCore=null;async function takeScreenshotForComponent(e,t,o,a,n,r){const i=r||config.cameraSettings[t][o];if(setWeatherTime(),await Delay(500),!camInfo||camInfo.zPos!==i.zPos||camInfo.fov!==i.fov){camInfo=i,cam&&(DestroyAllCams(!0),DestroyCam(cam,!0),cam=null),SetEntityRotation(ped,config.greenScreenRotation.x,config.greenScreenRotation.y,config.greenScreenRotation.z,0,!1),SetEntityCoordsNoOffset(ped,config.greenScreenPosition.x,config.greenScreenPosition.y,config.greenScreenPosition.z,!1,!1,!1),await Delay(50);const[e,t,o]=GetEntityCoords(ped),[a,n,r]=GetEntityForwardVector(ped),s={x:e+1.2*a,y:t+1.2*n,z:o+r+camInfo.zPos};cam=CreateCamWithParams("DEFAULT_SCRIPTED_CAMERA",s.x,s.y,s.z,0,0,0,camInfo.fov,!0,0),PointCamAtCoord(cam,e,t,o+camInfo.zPos),SetCamActive(cam,!0),RenderScriptCams(!0,!1,0,!0,!1,0)}await Delay(50),SetEntityRotation(ped,camInfo.rotation.x,camInfo.rotation.y,camInfo.rotation.z,2,!1),emitNet("takeScreenshot",`${e}_${"PROPS"==t?"prop_":""}${o}_${a}${n?`_${n}`:""}`,"clothing"),await Delay(2e3)}async function takeScreenshotForObject(e,t){setWeatherTime(),await Delay(500),cam&&(DestroyAllCams(!0),DestroyCam(cam,!0),cam=null);let[[o,a,n],[r,i,s]]=GetModelDimensions(t),c={x:r-o,y:i-a,z:s-n},l=Math.min(Math.max(c.x,c.z)/.15*10,60);const[d,m,g]=GetEntityCoords(e,!1),[p,S,y]=GetEntityForwardVector(e),f={x:d+(o+r)/2,y:m+(a+i)/2,z:g+(n+s)/2},h={x:f.x+1.2*p+Math.max(c.x,c.z)/2,y:f.y+1.2*S+Math.max(c.x,c.z)/2,z:f.z+y};console.log(c.x,c.z),cam=CreateCamWithParams("DEFAULT_SCRIPTED_CAMERA",h.x,h.y,h.z,0,0,0,l,!0,0),PointCamAtCoord(cam,f.x,f.y,f.z),SetCamActive(cam,!0),RenderScriptCams(!0,!1,0,!0,!1,0),await Delay(50),emitNet("takeScreenshot",`${t}`,"objects"),await Delay(2e3)}async function takeScreenshotForVehicle(e,t,o){setWeatherTime(),await Delay(500),cam&&(DestroyAllCams(!0),DestroyCam(cam,!0),cam=null);let[[a,n,r],[i,s,c]]=GetModelDimensions(t),l={x:i-a,y:s-n,z:c-r},d=Math.min(Math.max(l.x,l.y,l.z)/.15*10,60);const[m,g,p]=GetEntityCoords(e,!1),S={x:m+(a+i)/2,y:g+(n+s)/2,z:p+(r+c)/2};let y={x:S.x+(Math.max(l.x,l.y,l.z)+2)*Math.cos(340),y:S.y+(Math.max(l.x,l.y,l.z)+2)*Math.sin(340),z:S.z+l.z/2};cam=CreateCamWithParams("DEFAULT_SCRIPTED_CAMERA",y.x,y.y,y.z,0,0,0,d,!0,0),PointCamAtCoord(cam,S.x,S.y,S.z),SetCamActive(cam,!0),RenderScriptCams(!0,!1,0,!0,!1,0),await Delay(50),emitNet("takeScreenshot",`${o}`,"vehicles"),await Delay(2e3)}function SetPedOnGround(){const[e,t,o]=GetEntityCoords(ped,!1),[a,n]=GetGroundZFor_3dCoord(e,t,o,0,!1);SetEntityCoords(ped,e,t,n,!1,!1,!1,!1)}function ClearAllPedProps(){for(const e of Object.keys(config.cameraSettings.PROPS))ClearPedProp(ped,parseInt(e))}async function ResetPedComponents(){config.debug&&console.log("DEBUG: Resetting Ped Components"),SetPedDefaultComponentVariation(ped),await Delay(150),SetPedComponentVariation(ped,0,0,1,0),SetPedComponentVariation(ped,1,0,0,0),SetPedComponentVariation(ped,2,-1,0,0),SetPedComponentVariation(ped,7,0,0,0),SetPedComponentVariation(ped,5,0,0,0),SetPedComponentVariation(ped,6,-1,0,0),SetPedComponentVariation(ped,9,0,0,0),SetPedComponentVariation(ped,3,-1,0,0),SetPedComponentVariation(ped,8,-1,0,0),SetPedComponentVariation(ped,4,-1,0,0),SetPedComponentVariation(ped,11,-1,0,0),SetPedHairColor(ped,45,15),ClearAllPedProps()}function setWeatherTime(){config.debug&&console.log("DEBUG: Setting Weather & Time"),SetRainLevel(0),SetWeatherTypePersist("EXTRASUNNY"),SetWeatherTypeNow("EXTRASUNNY"),SetWeatherTypeNowPersist("EXTRASUNNY"),NetworkOverrideClockTime(18,0,0),NetworkOverrideClockMillisecondsPerGameMinute(1e6)}function stopWeatherResource(){return config.debug&&console.log("DEBUG: Stopping Weather Resource"),"started"==GetResourceState("qb-weathersync")||"started"==GetResourceState("qbx_weathersync")?(TriggerEvent("qb-weathersync:client:DisableSync"),!0):"started"==GetResourceState("weathersync")?(TriggerEvent("weathersync:toggleSync"),!0):"started"==GetResourceState("esx_wsync")?(SendNUIMessage({error:"weathersync"}),!1):"started"==GetResourceState("cd_easytime")?(TriggerEvent("cd_easytime:PauseSync",!1),!0):"started"!=GetResourceState("vSync")&&"started"!=GetResourceState("Renewed-Weathersync")||(TriggerEvent("vSync:toggle",!1),!0)}function startWeatherResource(){config.debug&&console.log("DEBUG: Starting Weather Resource again"),"started"==GetResourceState("qb-weathersync")||"started"==GetResourceState("qbx_weathersync")?TriggerEvent("qb-weathersync:client:EnableSync"):"started"==GetResourceState("weathersync")?TriggerEvent("weathersync:toggleSync"):"started"==GetResourceState("cd_easytime")?TriggerEvent("cd_easytime:PauseSync",!0):"started"!=GetResourceState("vSync")&&"started"!=GetResourceState("Renewed-Weathersync")||TriggerEvent("vSync:toggle",!0)}async function LoadComponentVariation(e,t,o,a){for(a=a||0,config.debug&&console.log(`DEBUG: Loading Component Variation: ${t} ${o} ${a}`),SetPedPreloadVariationData(e,t,o,a);!HasPedPreloadVariationDataFinished(e);)await Delay(50);SetPedComponentVariation(e,t,o,a,0)}async function LoadPropVariation(e,t,o,a){for(a=a||0,config.debug&&console.log(`DEBUG: Loading Prop Variation: ${t} ${o} ${a}`),SetPedPreloadPropData(e,t,o,a);!HasPedPreloadPropDataFinished(e);)await Delay(50);ClearPedProp(e,t),SetPedPropIndex(e,t,o,a,0)}function createGreenScreenVehicle(e,t){return new Promise((async(o,a)=>{config.debug&&console.log(`DEBUG: Spawning Vehicle ${t}`);const n=setTimeout((()=>{o(null)}),config.vehicleSpawnTimeout);if(!HasModelLoaded(e))for(RequestModel(e);!HasModelLoaded(e);)await Delay(100);const r=CreateVehicle(e,config.greenScreenVehiclePosition.x,config.greenScreenVehiclePosition.y,config.greenScreenVehiclePosition.z,0,!0,!0);0===r&&(clearTimeout(n),o(null)),clearTimeout(n),o(r)}))}config.useQBVehicles&&(QBCore=exports[config.coreResourceName].GetCoreObject()),RegisterCommand("screenshot",(async(e,t)=>{const o=[GetHashKey("mp_m_freemode_01"),GetHashKey("mp_f_freemode_01")];if(SendNUIMessage({start:!0}),stopWeatherResource()){DisableIdleCamera(!0),await Delay(100);for(const e of o)if(IsModelValid(e)){if(!HasModelLoaded(e))for(RequestModel(e);!HasModelLoaded(e);)await Delay(100);SetPlayerModel(playerId,e),await Delay(150),SetModelAsNoLongerNeeded(e),await Delay(150),ped=PlayerPedId();const t=e===GetHashKey("mp_m_freemode_01")?"male":"female";SetEntityRotation(ped,config.greenScreenRotation.x,config.greenScreenRotation.y,config.greenScreenRotation.z,0,!1),SetEntityCoordsNoOffset(ped,config.greenScreenPosition.x,config.greenScreenPosition.y,config.greenScreenPosition.z,!1,!1,!1),FreezeEntityPosition(ped,!0),await Delay(50),SetPlayerControl(playerId,!1),interval=setInterval((()=>{ClearPedTasksImmediately(ped)}),1);for(const e of Object.keys(config.cameraSettings))for(const o of Object.keys(config.cameraSettings[e])){await ResetPedComponents(),await Delay(150);const a=parseInt(o);if("CLOTHING"===e){const o=GetNumberOfPedDrawableVariations(ped,a);for(let n=0;n<o;n++){const r=GetNumberOfPedTextureVariations(ped,a,n);if(SendNUIMessage({type:config.cameraSettings[e][a].name,value:n,max:o}),config.includeTextures)for(let o=0;o<r;o++)await LoadComponentVariation(ped,a,n,o),await takeScreenshotForComponent(t,e,a,n,o);else await LoadComponentVariation(ped,a,n),await takeScreenshotForComponent(t,e,a,n)}}else if("PROPS"===e){const o=GetNumberOfPedPropDrawableVariations(ped,a);for(let n=0;n<o;n++){const r=GetNumberOfPedPropTextureVariations(ped,a,n);if(SendNUIMessage({type:config.cameraSettings[e][a].name,value:n,max:o}),config.includeTextures)for(let o=0;o<r;o++)await LoadPropVariation(ped,a,n,o),await takeScreenshotForComponent(t,e,a,n,o);else await LoadPropVariation(ped,a,n),await takeScreenshotForComponent(t,e,a,n)}}}SetModelAsNoLongerNeeded(e),SetPlayerControl(playerId,!0),FreezeEntityPosition(ped,!1),clearInterval(interval)}SetPedOnGround(),startWeatherResource(),SendNUIMessage({end:!0}),DestroyAllCams(!0),DestroyCam(cam,!0),RenderScriptCams(!1,!1,0,!0,!1,0),camInfo=null,cam=null}})),RegisterCommand("customscreenshot",(async(e,t)=>{const o=t[2].toUpperCase(),a=parseInt(t[0]);let n="all"==t[1].toLowerCase()?t[1].toLowerCase():parseInt(t[1]),r="all"==t[1].toLowerCase()?t[1].toLowerCase():parseInt(t[1]);const i=t[3].toLowerCase();let s,c;if(c="male"==i?[GetHashKey("mp_m_freemode_01")]:"female"==i?[GetHashKey("mp_f_freemode_01")]:[GetHashKey("mp_m_freemode_01"),GetHashKey("mp_f_freemode_01")],null!=t[4]){let e="";for(let o=4;o<t.length;o++)e+=t[o]+" ";e=JSON.parse(e)}if(stopWeatherResource()){DisableIdleCamera(!0),await Delay(100);for(const e of c)if(IsModelValid(e)){if(!HasModelLoaded(e))for(RequestModel(e);!HasModelLoaded(e);)await Delay(100);SetPlayerModel(playerId,e),await Delay(150),SetModelAsNoLongerNeeded(e),await Delay(150),ped=PlayerPedId(),interval=setInterval((()=>{ClearPedTasksImmediately(ped)}),1);const t=e===GetHashKey("mp_m_freemode_01")?"male":"female";if(SetEntityRotation(ped,config.greenScreenRotation.x,config.greenScreenRotation.y,config.greenScreenRotation.z,0,!1),SetEntityCoordsNoOffset(ped,config.greenScreenPosition.x,config.greenScreenPosition.y,config.greenScreenPosition.z,!1,!1,!1),FreezeEntityPosition(ped,!0),await Delay(50),SetPlayerControl(playerId,!1),ResetPedComponents(),await Delay(150),"all"==n){if(SendNUIMessage({start:!0}),"CLOTHING"===o){const e=GetNumberOfPedDrawableVariations(ped,a);for(n=0;n<e;n++){const r=GetNumberOfPedTextureVariations(ped,a,n);if(SendNUIMessage({type:config.cameraSettings[o][a].name,value:n,max:e}),config.includeTextures)for(let e=0;e<r;e++)await LoadComponentVariation(ped,a,n,e),await takeScreenshotForComponent(t,o,a,n,e,s);else await LoadComponentVariation(ped,a,n),await takeScreenshotForComponent(t,o,a,n,null,s)}}else if("PROPS"===o){const e=GetNumberOfPedPropDrawableVariations(ped,a);for(r=0;r<e;r++){const n=GetNumberOfPedPropTextureVariations(ped,a,r);if(SendNUIMessage({type:config.cameraSettings[o][a].name,value:r,max:e}),config.includeTextures)for(let e=0;e<n;e++)await LoadPropVariation(ped,a,r,e),await takeScreenshotForComponent(t,o,a,r,e,s);else await LoadPropVariation(ped,a,r),await takeScreenshotForComponent(t,o,a,r,null,s)}}}else if(!isNaN(n))if("CLOTHING"===o){const e=GetNumberOfPedTextureVariations(ped,a,n);if(config.includeTextures)for(let r=0;r<e;r++)await LoadComponentVariation(ped,a,n,r),await takeScreenshotForComponent(t,o,a,n,r,s);else await LoadComponentVariation(ped,a,n),await takeScreenshotForComponent(t,o,a,n,null,s)}else if("PROPS"===o){const e=GetNumberOfPedPropTextureVariations(ped,a,r);if(config.includeTextures)for(let n=0;n<e;n++)await LoadPropVariation(ped,a,r,n),await takeScreenshotForComponent(t,o,a,r,n,s);else await LoadPropVariation(ped,a,r),await takeScreenshotForComponent(t,o,a,r,null,s)}SetPlayerControl(playerId,!0),FreezeEntityPosition(ped,!1),clearInterval(interval)}SetPedOnGround(),startWeatherResource(),SendNUIMessage({end:!0}),DestroyAllCams(!0),DestroyCam(cam,!0),RenderScriptCams(!1,!1,0,!0,!1,0),camInfo=null,cam=null}})),RegisterCommand("screenshotobject",(async(e,t)=>{let o=isNaN(Number(t[0]))?GetHashKey(t[0]):Number(t[0]);const a=GetPlayerPed(-1);if(IsWeaponValid(o)&&(o=GetWeapontypeModel(o)),!stopWeatherResource())return;if(DisableIdleCamera(!0),await Delay(100),!IsModelValid(o))return void console.log("ERROR: Invalid object model");if(!HasModelLoaded(o))for(RequestModel(o);!HasModelLoaded(o);)await Delay(100);SetEntityCoords(a,config.greenScreenHiddenSpot.x,config.greenScreenHiddenSpot.y,config.greenScreenHiddenSpot.z,!1,!1,!1),SetPlayerControl(playerId,!1),config.debug&&console.log(`DEBUG: Spawning Object ${o}`);const n=CreateObjectNoOffset(o,config.greenScreenPosition.x,config.greenScreenPosition.y,config.greenScreenPosition.z,!1,!0,!0);SetEntityRotation(n,config.greenScreenRotation.x,config.greenScreenRotation.y,config.greenScreenRotation.z,0,!1),FreezeEntityPosition(n,!0),await Delay(50),await takeScreenshotForObject(n,o),DeleteEntity(n),SetPlayerControl(playerId,!0),SetModelAsNoLongerNeeded(o),startWeatherResource(),DestroyAllCams(!0),DestroyCam(cam,!0),RenderScriptCams(!1,!1,0,!0,!1,0),cam=null})),RegisterCommand("screenshotvehicle",(async(e,t)=>{const o=config.useQBVehicles&&null!=QBCore?Object.keys(QBCore.Shared.Vehicles):GetAllVehicleModels(),a=PlayerPedId(),n=t[0].toLowerCase(),r=t[1]?parseInt(t[1]):null,i=t[2]?parseInt(t[2]):null;if(stopWeatherResource()){if(DisableIdleCamera(!0),SetEntityCoords(a,config.greenScreenHiddenSpot.x,config.greenScreenHiddenSpot.y,config.greenScreenHiddenSpot.z,!1,!1,!1),SetPlayerControl(playerId,!1),ClearAreaOfVehicles(config.greenScreenPosition.x,config.greenScreenPosition.y,config.greenScreenPosition.z,10,!1,!1,!1,!1,!1),await Delay(100),"all"===n){SendNUIMessage({start:!0});for(const e of o){const t=GetHashKey(e);if(!IsModelValid(t))continue;const a=GetVehicleClassFromName(t);if(!config.includedVehicleClasses[a]){SetModelAsNoLongerNeeded(t);continue}SendNUIMessage({type:e,value:o.indexOf(e)+1,max:o.length+1});const n=await createGreenScreenVehicle(t,e);0!==n&&null!==n?(SetEntityRotation(n,config.greenScreenVehicleRotation.x,config.greenScreenVehicleRotation.y,config.greenScreenVehicleRotation.z,0,!1),FreezeEntityPosition(n,!0),SetVehicleWindowTint(n,1),SetVehicleDirtLevel(n,0),WashDecalsFromVehicle(n,1),r&&SetVehicleColours(n,r,i||r),await Delay(50),await takeScreenshotForVehicle(n,t,e),DeleteEntity(n),SetModelAsNoLongerNeeded(t)):(SetModelAsNoLongerNeeded(t),console.log(`ERROR: Could not spawn vehicle. Broken Vehicle: ${e}`))}SendNUIMessage({end:!0})}else{const e=n,t=GetHashKey(e);if(IsModelValid(t)){SendNUIMessage({type:e,value:o.indexOf(e)+1,max:o.length+1});const a=await createGreenScreenVehicle(t,e);if(0===a||null===a)return SetModelAsNoLongerNeeded(t),void console.log(`ERROR: Could not spawn vehicle. Broken Vehicle: ${e}`);SetEntityRotation(a,config.greenScreenVehicleRotation.x,config.greenScreenVehicleRotation.y,config.greenScreenVehicleRotation.z,0,!1),FreezeEntityPosition(a,!0),SetVehicleWindowTint(a,1),SetVehicleDirtLevel(a,0),WashDecalsFromVehicle(a,1),r&&SetVehicleColours(a,r,i||r),await Delay(50),await takeScreenshotForVehicle(a,t,e),DeleteEntity(a),SetModelAsNoLongerNeeded(t)}else console.log("ERROR: Invalid vehicle model")}SetPlayerControl(playerId,!0),startWeatherResource(),DestroyAllCams(!0),DestroyCam(cam,!0),RenderScriptCams(!1,!1,0,!0,!1,0),cam=null}})),setImmediate((()=>{emit("chat:addSuggestions",[{name:"/screenshot",help:"generate clothing screenshots"},{name:"/customscreenshot",help:"generate custom cloting screenshots",params:[{name:"component",help:"The clothing component to take a screenshot of"},{name:"drawable/all",help:"The drawable variation to take a screenshot of"},{name:"props/clothing",help:"PROPS or CLOTHING"},{name:"male/female/both",help:"The gender to take a screenshot of"},{name:"camera settings",help:"The camera settings to use for the screenshot (optional)"}]},{name:"/screenshotobject",help:"generate object screenshots",params:[{name:"object",help:"The object hash to take a screenshot of"}]},{name:"/screenshotvehicle",help:"generate vehicle screenshots",params:[{name:"model/all",help:"The vehicle model or 'all' to take a screenshot of all vehicles"},{name:"primarycolor",help:"The primary vehicle color to take a screenshot of (optional) See: https://wiki.rage.mp/index.php?title=Vehicle_Colors"},{name:"secondarycolor",help:"The secondary vehicle color to take a screenshot of (optional) See: https://wiki.rage.mp/index.php?title=Vehicle_Colors"}]}])})),on("onResourceStop",(e=>{GetCurrentResourceName()==e&&(startWeatherResource(),clearInterval(interval),SetPlayerControl(playerId,!0),FreezeEntityPosition(ped,!1))}));